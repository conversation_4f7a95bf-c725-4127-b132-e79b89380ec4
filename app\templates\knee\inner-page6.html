<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/html">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Artikon</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" type="text/css" href="../static/css/bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="../static/css/style.css">
    <link rel="stylesheet" type="text/css" href="../static/css/camera.css">
    <script src="../static/js/camera.js" defer></script>
</head>

<body>
    <div class="tbl">
        <div class="tbl-cell">
            <div class="content-area">
                <div class="head_wrap">
                    <div class="header">
                        <div class="first_head head_bg">Welcome To <a href="../index.html"><span class="bold">Artikon</span> <span
                                class="font-color">AGS</span></a></div>
                        <div class="second_head head_bg">
                            <div class="inner_head one">
                                <span><img src="../static/images/calendar.png" /></span><span id="date">07/01/2025</span>
                            </div>
                            <div class="inner_head">
                                <span><img src="../static/images/time.png" /></span><span id="time">18:37:05</span>
                            </div>
                        </div>
                        <div class="third_head head_bg">
                            <span><img src="../static/images/settings.png" /></span>Settings
                        </div>
                    </div>
                </div>
                <div class="middle_section_wrap multiple_page">
                    <div class="middle_section bone_cut resect_proximal">
                        <div class="profile">
                            <ul class="border-0">
                                <li>Assess initial leg alignment and balance</li>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center asses-box">
                                        <div class="col-8">
                                            <div class="row">
                                                <div class="col-6 position-relative">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00000.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00001.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00002.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00003.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00004.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00005.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00006.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00007.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00008.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00009.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00010.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00011.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00012.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00013.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00014.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00015.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00016.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00017.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00018.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00019.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00020.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00021.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00022.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00023.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00024.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00025.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00026.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00027.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00028.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00029.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00030.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00031.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00032.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00033.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00034.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00035.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00036.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00037.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00038.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00039.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00040.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00041.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00042.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00043.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00044.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00045.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00046.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00047.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00048.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00049.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00050.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00051.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00052.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00053.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00054.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00055.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00056.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00057.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00058.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00059.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00060.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00061.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00062.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00063.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00064.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00065.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00066.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00067.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00068.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00069.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00070.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00071.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00072.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00073.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00074.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00075.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00076.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00077.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00078.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00079.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00080.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00081.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00082.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00083.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00084.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00085.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00086.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00087.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00088.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00089.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00090.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00091.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00092.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00093.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00094.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00095.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00096.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00097.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00098.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00099.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00100.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00101.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00102.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00103.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00104.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00105.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00106.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00107.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00108.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00109.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00110.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00111.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00112.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00113.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00114.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00115.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00116.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00117.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00118.png" alt="" class="d-none">
													<img src="../static/images/bone/front/LEG_BONE_V3_FRONT_00119.png" alt="" class="d-none">

                                                   <div class="Bone-text d-none">
                                                        <button class="input-btn d-flex align-items-center justify-content-between">
                                                            <span id="textbox1"></span>
                                                            <sup class="fs-14">0</sup>
                                                            <span class="input-btn-text">alignment</span>
                                                        </button>
                                                    </div>
                                                </div>
                                                <div class="col-6 position-relative">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00000.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00001.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00002.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00003.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00004.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00005.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00006.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00007.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00008.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00009.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00010.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00011.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00012.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00013.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00014.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00015.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00016.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00017.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00018.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00019.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00020.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00021.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00022.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00023.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00024.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00025.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00026.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00027.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00028.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00029.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00030.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00031.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00032.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00033.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00034.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00035.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00036.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00037.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00038.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00039.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00040.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00041.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00042.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00043.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00044.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00045.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00046.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00047.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00048.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00049.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00050.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00051.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00052.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00053.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00054.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00055.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00056.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00057.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00058.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00059.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00060.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00061.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00062.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00063.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00064.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00065.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00066.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00067.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00068.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00069.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00070.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00071.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00072.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00073.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00074.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00075.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00076.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00077.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00078.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00079.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00080.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00081.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00082.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00083.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00084.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00085.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00086.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00087.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00088.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00089.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00090.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00091.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00092.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00093.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00094.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00095.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00096.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00097.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00098.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00099.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00100.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00101.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00102.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00103.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00104.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00105.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00106.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00107.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00108.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00109.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00110.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00111.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00112.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00113.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00114.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00115.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00116.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00117.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00118.png" alt="" class="d-none">
													<img src="../static/images/bone/side/LEG_BONE_V3_SIDE_00119.png" alt="" class="d-none">

                                                    <div class="Bone-text d-none">
                                                            <button class="input-btn d-flex align-items-center justify-content-between">
                                                                <span id="textbox2"></span>
                                                                <sup class="fs-14">0</sup>
                                                                <span class="input-btn-text">alignment</span>
                                                            </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
										<div class="col-4">
										<canvas id="safeZoneChart" width="100" height="100"></canvas>
										<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
										<script>
										  const ctx = document.getElementById('safeZoneChart').getContext('2d');

										  // Initialize the chart
										  const chart = new Chart(ctx, {
											type: 'scatter',
											data: {
											  datasets: [
												// Dataset for left-side values (Points)
												{
												  label: 'Left Side Points',
												  data: [], // Left side values
												  backgroundColor: 'blue',
												  borderColor: 'blue',
												  borderWidth: 2,
												  pointRadius: 3,
												  fill: false,
												  showLine: false, // Disable line for the points
												},
												// Dataset for left-side lines (Lines)
												{
												  //label: 'Left Side Line',
												  label: '',
												  data: [], // Left side values
												  backgroundColor: 'blue',
												  borderColor: 'blue',
												  borderWidth: 2,
												  fill: false, // Disable filling
												  showLine: true, // Enable line
												  tension: 0, // Optional: controls the line smoothness
												},
												// Dataset for right-side values (Points)
												{
												  label: 'Right Side Points',
												  data: [], // Right side values
												  backgroundColor: '#b7410e',
												  borderColor: '#b7410e',
												  borderWidth: 2,
												  pointRadius: 3,
												  fill: false,
												  showLine: false, // Disable line for the points
												},
												// Dataset for right-side lines (Lines)
												{
												  //label: 'Right Side Line',
												  label: '',
												  data: [], // Right side values
												  backgroundColor: '#b7410e',
												  borderColor: '#b7410e',
												  borderWidth: 2,
												  fill: false, // Disable filling
												  showLine: true, // Enable line
												  tension: 0, // Optional: controls the line smoothness
												},
											  ],
											},
											options: {
											  scales: {
												x: {
												  title: {
													display: true,
													text: '    L            Gaps (mm)               M   ',
													color: 'white',
												  },
												  ticks: {
													color: '#fff',
													stepSize: 5,
												  },
												  min: -30,
												  max: 30,
												  grid: {
													drawOnChartArea: true,
													drawTicks: true,
												  },
												},
												y: {
												  title: {
													display: true,
													text: 'ROM (degrees)',
													color: 'white',
												  },
												  ticks: {
													color: '#fff',
													stepSize: 5,
												  },
												  position: 'center',
												  min: -10,
												  max: 120,
												  grid: {
													drawTicks: true,
												  },
												},
											  },
											  plugins: {
												legend: {
												  position: 'bottom',
												  labels: {
													color: 'white',
												  },
												},
											  },
											  responsive: true,
											  maintainAspectRatio: true,
											  layout: {
												padding: {
												  left: 5,
												  right: 5,
												  top: 5,
												  bottom: 5,
												},
											  },
											  interaction: {
												mode: 'nearest',
												axis: 'xy',
											  },
											},
										  });

										  /* Function to fetch data from the backend
										  async function fetchData() {
											const response = await fetch('http://127.0.0.1:8000/get-graph_data');
											const data = await response.json();
											return data;
										  }

										  // Function to update the chart
										  async function updateChart() {
											const data = await fetchData();

											// Clear previous data in the chart datasets
											chart.data.datasets[0].data = []; // Clear left side points
											chart.data.datasets[1].data = []; // Clear left side line
											chart.data.datasets[2].data = []; // Clear right side points
											chart.data.datasets[3].data = []; // Clear right side line

											// Prepare left and right data arrays
											let leftData = [];
											let rightData = [];

											// Populate new data for left side and right side
											data.left.forEach(item => {
												leftData.push({ x: item.x, y: item.y });
											});

											data.right.forEach(item => {
												rightData.push({ x: item.x, y: item.y });
											});

											// Sort data by x value to avoid spider web effect
											leftData.sort((a, b) => a.y - b.y);
											rightData.sort((a, b) => a.y - b.y);

											// Update chart datasets with sorted data
											chart.data.datasets[0].data = leftData; // Left side points
											chart.data.datasets[1].data = leftData; // Left side line (same as points)
											chart.data.datasets[2].data = rightData; // Right side points
											chart.data.datasets[3].data = rightData; // Right side line (same as points)
											// Update the chart
											chart.update();
										  }
										  // Trigger chart resize when window is resized
										  window.addEventListener('resize', function () {
											chart.resize();
										  });
										  // Update chart every second
										  //setInterval(updateChart, 1000);*/
										</script>





                                    </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
                <div class="footer_wrap">
                    <div class="footer">

						<div class="bottom_btn">
							<div class="blank blank-none"></div>
								<div class="btn">
									<a id="backBtn"  href="tkr-screen-2.html">
										<span class="mr-20"><img src="../static/images/left-arrow.png" /></span>Back
									</a>
								</div>
								<div class="btn">
									<a id="nextBtn" href="tkr-screen-3.html"><span class="mr-20">Next</span><img src="../static/images/right-arrow.png" /></a>
							</div>
						</div>

                        <ul class="align-items-center">
                            <li class="copy_right">
                                © <span id="year">2023</span> Artikon AGS
                                <span class="top_txt">Auto Guided Surgery</span>
                            </li>
                            <li>
                                <ul class="bottom-icon">
                                    <li><img src="../static/images/icon/icon-1.png" class="img-fluid icon-img" alt=""></li>
                                    <li><img src="../static/images/icon/icon-2.png" class="img-fluid icon-img active" alt=""></li>
                                    <li><img src="../static/images/icon/icon-3.png" class="img-fluid icon-img" alt=""></li>
                                    <li><img src="../static/images/icon/icon-4.png" class="img-fluid icon-img" alt=""></li>
                                </ul>
                            </li>
                            <li class="footer_btn_one">
                                <a href="#">
                                    <!-- <span><img src="../static/images/camera-video.png" /></span>Cemera -->
                                    <div class="btn-group" role="group" aria-label="Basic example">
                                        <button type="button" class="btn first"><img
                                                src="../static/images/camera-video.png" /></button>
                                        <button type="button" class="btn second">F</button>
                                        <button type="button" class="btn third">T</button>
                                    </div>
                                </a>
                            </li>
                            <li class="footer_btn_two">
                                <a href="#">
								<span><img src="/static/images/home.png" id="home" alt="home Image"></span>Main Menu
								</a>
								<script>
										document.getElementById('home').addEventListener('click', function(event) {
											event.preventDefault();  // Prevent default anchor behavior
											window.location.href = '../landing-page.html';  // Redirect to root path
										});
								</script>
                            </li>
							<li class="footer_btn_three">
								<a href="#" id="powerbuttonLink">
									<span><img src="../static/images/union.png" id="powerbutton" alt="Union Image" /></span>
								</a>
								<script>
									document.getElementById('powerbuttonLink').addEventListener('click', function(event) {
										event.preventDefault();  // Prevent default anchor behavior
										window.location.href = '/';  // Redirect to root path
									});
								</script>
							</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
<!-- Camera Modal -->
    <div id="cameraModal" class="camera-modal">
        <div class="camera-modal-content">
            <span class="close-camera">&times;</span>
            <h2>Video Stream</h2>
            <img id="videoStream" />
        </div>
    </div>
</body>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<script src="../static/js/common.js"></script>
<script>


	function handleNavigation(event) {
    event.preventDefault();
    const targetUrl = event.currentTarget.href;

    if (socket && socket.readyState === WebSocket.OPEN) {
        socket.send(JSON.stringify({ file: " " }));
        socket.addEventListener('close', function () {
            window.location.href = targetUrl;
        }, { once: true });

        socket.close();
    } else {
        window.location.href = targetUrl;
    }
}


    let socket;
    var reconnectInterval = 1000;
    var lastShownFrontBone = null;
    var lastShownSideBone = null;

    function processData(jsonData) {
        if (jsonData.Graph_data) {
            const filteredResults = jsonData.Graph_data.filtered_results;
            const angles = jsonData.angles;
            console.log(filteredResults);
            console.log(angles);

            // Update textboxes with angles
            if (angles) {
                const alignmentText = document.getElementById('textbox1');
                const anteversionText = document.getElementById('textbox2');

                if (alignmentText) {
                    alignmentText.innerHTML = angles.Alignement + '°';
                }
                if (anteversionText) {
                    anteversionText.innerHTML = angles.Anteversion + '°';
                }

                // Show the textboxes
                $('.Bone-text').removeClass('d-none');
            }

            // Prepare data for the graph
            let leftData = [];
            let rightData = [];

            // Process filtered results
            Object.entries(filteredResults).forEach(([angle, values]) => {
                const y = parseFloat(angle);
                leftData.push({
                    x: parseFloat(values.left),
                    y: y
                });
                rightData.push({
                    x: parseFloat(values.right),
                    y: y
                });
            });

            // Sort data points by y value
            leftData.sort((a, b) => a.y - b.y);
            rightData.sort((a, b) => a.y - b.y);

            // Update chart datasets
            chart.data.datasets[0].data = leftData;
            chart.data.datasets[1].data = leftData;
            chart.data.datasets[2].data = rightData;
            chart.data.datasets[3].data = rightData;
            chart.update();

            // Update bone images
<!--            const frontImageId = `LEG_BONE_V3_FRONT_${String(angles.Alignement).padStart(5, '0')}`;-->
            const sideImageId = `LEG_BONE_V3_SIDE_${String(angles.Anteversion).padStart(5, '0')}`;


			const frontImageId = angles.Alignement < 0
			? "LEG_BONE_V3_FRONT_00000"
			: `LEG_BONE_V3_FRONT_${String(angles.Alignement).padStart(5, '0')}`;

            if (lastShownFrontBone) {
                $(`img[src*='${lastShownFrontBone}']`).addClass('d-none');
            }
            $(`img[src*='${frontImageId}']`).removeClass('d-none');
            lastShownFrontBone = frontImageId;

            if (lastShownSideBone) {
                $(`img[src*='${lastShownSideBone}']`).addClass('d-none');
            }
            $(`img[src*='${sideImageId}']`).removeClass('d-none');
            lastShownSideBone = sideImageId;
        }
    }

    function initWebSocket() {
        if (!socket || socket.readyState !== WebSocket.OPEN) {
            const currentUrl = window.location.href;
            const pageName = currentUrl.substring(currentUrl.lastIndexOf('/') + 1).split('?')[0];
            socket = new WebSocket('ws://127.0.0.1:8000/ws');

            socket.onopen = function(event) {
                console.log('Socket opened');
                socket.send(JSON.stringify({ file: pageName }));
            };

            socket.onmessage = function(event) {
                const values = JSON.parse(event.data);
                console.log(event.data);
                processData(values);
            };

            socket.onerror = function(error) {
                console.error('Socket error:', error);
            };

            socket.onclose = function(event) {
                console.log('Socket closed:', event);
                // Attempt to reconnect after a delay
                setTimeout(function() {
                    console.log('Attempting to reconnect...');
                    initWebSocket();
                }, reconnectInterval);
            };

            document.getElementById('backBtn')?.addEventListener('click', handleNavigation);
    		document.getElementById('nextBtn')?.addEventListener('click', handleNavigation);

        }
    }

    // Start processing test data when the window loads
    window.onload = function() {
        initWebSocket();


    };
</script>


</html>